/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024-08-02
  * @brief   STM32F4控制DAC904产生信号 - 简化版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// DAC904高性能模块
#include "../Modules/Generation/dac904.h"
#include "../Modules/Generation/dds_dac904.h"
#include "../Modules/Generation/wave_tables_14bit.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// DAC904 DMA+波形表配置

// 真正的DMA高速波形表配置
#define WAVE_POINTS_PER_CYCLE 200              // 每周期波形点数 (5MHz时25MHz采样率)
#define MAX_DMA_SAMPLE_RATE 25000000           // 25MHz DMA采样率
static uint16_t dma_wave_buffer[WAVE_POINTS_PER_CYCLE];  // DMA波形缓冲区
static volatile bool dma_running = false;      // DMA运行状态

// DAC904技术优势：
// - 14位分辨率，165MHz最大采样率
// - 32位相位累加器，高精度频率控制
// - 并行数据接口，超高速数据传输
// - 1Hz-100kHz频率范围，适合音频和射频应用
// - 支持多种波形：正弦波、方波、三角波、锯齿波

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Generate_DMA_Wave_Buffer(uint32_t frequency, float amplitude);
void DAC904_True_DMA_Init(void);
void DAC904_True_DMA_Start(uint32_t frequency);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== DAC904真正DMA系统初始化 ==================== */
    Generate_DMA_Wave_Buffer(3000, 1.0f);  // 生成3kHz, 1V峰峰值波形
    DAC904_True_DMA_Init();                 // 初始化真正的DMA系统

    /* ==================== 系统就绪 ==================== */

    /* ==================== 系统稳定延时 ==================== */
    Delay_ms(100);  // 短暂延时让系统稳定



    /* ==================== 启动真正DMA信号生成 ==================== */
    // 启动3kHz正弦波，1V峰峰值 - 为5MHz做准备的架构
    DAC904_True_DMA_Start(3000);  // 3kHz测试

    while (1) {
        // DMA自动运行，CPU完全空闲
        __NOP();
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  生成DMA波形缓冲区
  * @param  frequency: 目标频率 (Hz)
  * @param  amplitude: 峰峰值 (V)
  * @retval None
  */
void Generate_DMA_Wave_Buffer(uint32_t frequency, float amplitude)
{
    for (uint32_t i = 0; i < WAVE_POINTS_PER_CYCLE; i++) {
        float angle = 2.0f * 3.14159265f * i / WAVE_POINTS_PER_CYCLE;
        float sine_val = sinf(angle);

        // 计算DAC值：amplitude峰峰值，2.5V偏移，14位DAC
        // amplitude V峰峰值 = ±(amplitude/2)V
        // 对应±(amplitude/2 * 16383/5) DAC值
        // 2.5V偏移 = 8192 DAC值
        float dac_range = (amplitude / 2.0f) * (16383.0f / 5.0f);
        uint16_t dac_val = (uint16_t)(8192 + dac_range * sine_val);

        // 确保在有效范围内
        if (dac_val > 16383) dac_val = 16383;

        dma_wave_buffer[i] = dac_val;
    }
}

/**
  * @brief  初始化DAC904真正DMA系统
  * @param  None
  * @retval None
  */
void DAC904_True_DMA_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

    // 初始化DAC904基本GPIO
    DAC904_Config_t dac_config = {
        .mode = DAC904_MODE_MANUAL,
        .clock_frequency = 0,
        .gpio_speed = DAC904_GPIO_SPEED_HIGH,
        .enable_clock_output = false  // DMA模式下手动控制时钟
    };
    DAC904_Init(&dac_config);

    // 配置TIM1作为DMA触发源 - 高速模式
    TIM_TimeBaseStructure.TIM_Period = 6;   // 168MHz/7 = 24MHz (接近25MHz目标)
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);

    // 使能TIM1 Update DMA请求
    TIM_DMACmd(TIM1, TIM_DMA_Update, ENABLE);

    // 配置DMA2 Stream5 Channel6 (TIM1_UP)
    DMA_DeInit(DMA2_Stream5);
    DMA_InitStructure.DMA_Channel = DMA_Channel_6;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&GPIOE->ODR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)dma_wave_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = WAVE_POINTS_PER_CYCLE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Enable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(DMA2_Stream5, &DMA_InitStructure);
}

/**
  * @brief  启动真正DMA高速信号生成
  * @param  frequency: 目标频率 (Hz)
  * @retval None
  */
void DAC904_True_DMA_Start(uint32_t frequency)
{
    // 计算所需的定时器频率
    // 目标：frequency Hz，每周期 WAVE_POINTS_PER_CYCLE 个点
    // 需要的DMA传输频率 = frequency * WAVE_POINTS_PER_CYCLE
    uint32_t required_dma_freq = frequency * WAVE_POINTS_PER_CYCLE;

    // 计算定时器周期
    // TIM1时钟 = 168MHz
    uint32_t timer_period = 168000000 / required_dma_freq;
    if (timer_period < 3) timer_period = 3;  // 最小周期限制
    if (timer_period > 65535) timer_period = 65535;  // 最大周期限制

    // 更新定时器周期
    TIM1->ARR = timer_period - 1;

    // 启动DMA和定时器
    DMA_Cmd(DMA2_Stream5, ENABLE);
    TIM_Cmd(TIM1, ENABLE);

    dma_running = true;

    // 启动指示
    for(int i = 0; i < 2; i++) {
        DAC904_WriteData(16383);
        Delay_ms(100);
        DAC904_WriteData(8192);
        Delay_ms(100);
    }
}











/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


