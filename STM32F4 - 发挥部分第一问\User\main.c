/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024-08-02
  * @brief   STM32F4控制DAC904产生信号 - 简化版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// DAC904高性能模块
#include "../Modules/Generation/dac904.h"
#include "../Modules/Generation/dds_dac904.h"
#include "../Modules/Generation/wave_tables_14bit.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// DAC904 DMA+波形表配置
static uint32_t current_frequency = 3000;      // 当前频率 (默认3kHz)
static Wave_Type_14bit_t current_wave_type = WAVE_TYPE_SINE;  // 当前波形 (默认正弦波)

// 高速波形表配置
#define WAVE_TABLE_SIZE 1000                    // 波形表大小
#define BASE_SAMPLE_RATE 10000000              // 10MHz基础采样率
static uint16_t sine_wave_table[WAVE_TABLE_SIZE];  // 正弦波表
static volatile uint32_t wave_index = 0;       // 当前波形表索引
static volatile uint32_t wave_step = 1000;     // 波形表步长 (频率控制)
static volatile uint32_t wave_accumulator = 0; // 相位累加器

// DAC904技术优势：
// - 14位分辨率，165MHz最大采样率
// - 32位相位累加器，高精度频率控制
// - 并行数据接口，超高速数据传输
// - 1Hz-100kHz频率范围，适合音频和射频应用
// - 支持多种波形：正弦波、方波、三角波、锯齿波

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DAC904_DDS_Init(void);
void Generate_Sine_Wave_Table(void);
void DAC904_DMA_Init(void);
void DAC904_DMA_Start(uint32_t frequency);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== DAC904 DMA系统初始化 ==================== */
    Generate_Sine_Wave_Table();  // 生成正弦波表
    DAC904_DMA_Init();           // 初始化DMA系统

    /* ==================== 系统就绪 ==================== */

    /* ==================== 基本DAC904测试 ==================== */
    // 延时让系统稳定
    Delay_ms(1000);

    // 首先测试基本DAC功能（不启动DDS）
    // 简单的电压测试
    DAC904_WriteData(0);      // 0V
    Delay_ms(2000);

    DAC904_WriteData(8192);   // 2.5V (中间值)
    Delay_ms(2000);

    DAC904_WriteData(16383);  // 5V (最大值)
    Delay_ms(2000);

    /* ==================== DDS正弦波测试 ==================== */
    // 启动DDS系统产生连续正弦波
    DDS_DAC904_Status_t dds_status = DDS_DAC904_Start();
    if (dds_status == DDS_DAC904_OK) {
        // DDS启动成功，LED指示（如果有的话）
        // 可以在这里添加LED闪烁或其他指示
        // DDS启动成功，让它运行5秒
        Delay_ms(5000);

        // 测试频率调整
        DDS_DAC904_SetFrequency(2000);  // 2kHz
        Delay_ms(3000);

        DDS_DAC904_SetFrequency(500);   // 500Hz
        Delay_ms(3000);

        DDS_DAC904_SetFrequency(1000);  // 回到1kHz
        Delay_ms(2000);
    } else {
        // DDS启动失败，使用手动正弦波
        // 错误指示：快速闪烁或其他方式
        DAC904_WriteData(16383);  // 最大值指示错误
        Delay_ms(100);
        DAC904_WriteData(0);      // 最小值
        Delay_ms(100);
        DAC904_WriteData(8192);   // 回到中间值
        Delay_ms(500);
        for (int cycle = 0; cycle < 3; cycle++) {  // 3个周期
            // 修复：使用完整的正弦波数据，4V峰峰值，2.5V偏移
            uint16_t sine_points[16] = {
                10650,  // 0°   = 2.5V + 2V*sin(0°)
                12236,  // 22.5°= 2.5V + 2V*sin(22.5°)
                13650,  // 45°  = 2.5V + 2V*sin(45°)
                14746,  // 67.5°= 2.5V + 2V*sin(67.5°)
                15383,  // 90°  = 2.5V + 2V*sin(90°)
                14746,  // 112.5°
                13650,  // 135°
                12236,  // 157.5°
                10650,  // 180°
                9064,   // 202.5°
                7650,   // 225°
                6554,   // 247.5°
                5917,   // 270° = 2.5V + 2V*sin(270°)
                6554,   // 292.5°
                7650,   // 315°
                9064    // 337.5°
            };

            for (int i = 0; i < 16; i++) {
                DAC904_WriteData(sine_points[i]);
                Delay_ms(20);  // 减少到20ms，总周期320ms ≈ 3.125Hz
            }
        }
    }

    /* ==================== 启动DMA高速信号生成 ==================== */
    // 启动3kHz正弦波，1V峰峰值输出
    DAC904_DMA_Start(3000);  // 3kHz

    while (1) {
        // DMA自动运行，CPU完全空闲
        __NOP();
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  生成正弦波表
  * @param  None
  * @retval None
  */
void Generate_Sine_Wave_Table(void)
{
    for (uint32_t i = 0; i < WAVE_TABLE_SIZE; i++) {
        float angle = 2.0f * 3.14159265f * i / WAVE_TABLE_SIZE;
        float sine_val = sinf(angle);

        // 1V峰峰值，2.5V偏移，14位DAC
        // 1V峰峰值 = ±0.5V，对应±1638 DAC值
        // 2.5V偏移 = 8192 DAC值
        uint16_t dac_val = (uint16_t)(8192 + 1638 * sine_val);

        // 确保在有效范围内
        if (dac_val > 16383) dac_val = 16383;
        if (dac_val < 0) dac_val = 0;

        sine_wave_table[i] = dac_val;
    }
}

/**
  * @brief  初始化DAC904高速波形表系统
  * @param  None
  * @retval None
  */
void DAC904_DMA_Init(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能TIM1时钟 (168MHz APB2)
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);

    // 初始化DAC904基本GPIO
    DAC904_Config_t dac_config = {
        .mode = DAC904_MODE_MANUAL,
        .clock_frequency = 0,
        .gpio_speed = DAC904_GPIO_SPEED_HIGH,
        .enable_clock_output = true  // 启用时钟输出
    };
    DAC904_Init(&dac_config);

    // 配置TIM1高速定时器
    TIM_TimeBaseStructure.TIM_Period = 16;  // 168MHz/17 ≈ 10MHz基础频率
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);

    // 使能定时器中断
    TIM_ITConfig(TIM1, TIM_IT_Update, ENABLE);

    // 配置NVIC - 最高优先级
    NVIC_InitStructure.NVIC_IRQChannel = TIM1_UP_TIM10_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  启动高速波形表信号生成
  * @param  frequency: 目标频率 (Hz)
  * @retval None
  */
void DAC904_DMA_Start(uint32_t frequency)
{
    // 计算波形表步长
    // 公式：step = (frequency * WAVE_TABLE_SIZE * 65536) / BASE_SAMPLE_RATE
    // 使用16位小数精度提高频率精度
    wave_step = (uint32_t)(((uint64_t)frequency * WAVE_TABLE_SIZE * 65536) / BASE_SAMPLE_RATE);

    // 重置相位累加器
    wave_accumulator = 0;
    wave_index = 0;

    // 启动定时器
    TIM_Cmd(TIM1, ENABLE);
}

/**
  * @brief  TIM1中断处理函数 - 高速波形表输出
  * @param  None
  * @retval None
  */
void TIM1_UP_TIM10_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM1, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(TIM1, TIM_IT_Update);

        // 高效的波形表查找和输出
        uint16_t dac_data = sine_wave_table[wave_index];

        // 直接写入DAC904 (最快方式)
        GPIOE->ODR = (GPIOE->ODR & ~0x3FFF) | (dac_data & 0x3FFF);

        // 产生时钟脉冲
        GPIOE->BSRRL = GPIO_Pin_14;  // CLK高
        __NOP(); __NOP(); __NOP(); __NOP();  // 延时
        GPIOE->BSRRH = GPIO_Pin_14;  // CLK低

        // 更新相位累加器和索引
        wave_accumulator += wave_step;
        wave_index = (wave_accumulator >> 16) % WAVE_TABLE_SIZE;
    }
}

/**
  * @brief  DAC904 DDS系统初始化
  * @param  None
  * @retval None
  */
void DAC904_DDS_Init(void)
{
    // 配置DAC904 DDS参数
    DDS_DAC904_Config_t dds_config = {
        .frequency = current_frequency,         // 3kHz
        .amplitude = 1.0f,                     // 1.0V峰峰值
        .offset = 2.5f,                        // 2.5V直流偏移 (单极性输出)
        .wave_type = current_wave_type,        // 正弦波
        .sample_rate = DDS_DAC904_SAMPLE_RATE_100K,  // 100kHz采样率 (确保系统稳定)
        .enable_continuous = true,             // 连续输出
        .enable_phase_sync = false             // 不需要相位同步
    };

    // 初始化DDS DAC904
    DDS_DAC904_Status_t status = DDS_DAC904_Init(&dds_config);
    if (status != DDS_DAC904_OK) {
        // 初始化失败，跳过DDS功能，继续执行基本测试
        // 这样可以测试DAC904基本功能
    } else {
        // 立即启动DDS输出
        status = DDS_DAC904_Start();
        if (status != DDS_DAC904_OK) {
            // 启动失败，但继续执行
        }
    }
}







/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


