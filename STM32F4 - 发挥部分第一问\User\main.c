/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024-08-02
  * @brief   STM32F4控制DAC904产生信号 - 简化版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// DAC904高性能模块
#include "../Modules/Generation/dac904.h"
#include "../Modules/Generation/dds_dac904.h"
#include "../Modules/Generation/wave_tables_14bit.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// DAC904 DDS配置变量
static uint32_t current_frequency = 3000;      // 当前频率 (默认3kHz)
static Wave_Type_14bit_t current_wave_type = WAVE_TYPE_SINE;  // 当前波形 (默认正弦波)
// 注意：current_amplitude变量已移除，直接在函数中使用常量

// DAC904技术优势：
// - 14位分辨率，165MHz最大采样率
// - 32位相位累加器，高精度频率控制
// - 并行数据接口，超高速数据传输
// - 1Hz-100kHz频率范围，适合音频和射频应用
// - 支持多种波形：正弦波、方波、三角波、锯齿波

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void DAC904_DDS_Init(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== DAC904 DDS系统初始化 ==================== */
    DAC904_DDS_Init();

    /* ==================== 调试状态检查 ==================== */
    DAC904_Debug_Status();

    /* ==================== 基本DAC904测试 ==================== */
    // 延时让系统稳定
    Delay_ms(1000);

    // 首先测试基本DAC功能（不启动DDS）
    // 简单的电压测试
    DAC904_WriteData(0);      // 0V
    Delay_ms(2000);

    DAC904_WriteData(8192);   // 2.5V (中间值)
    Delay_ms(2000);

    DAC904_WriteData(16383);  // 5V (最大值)
    Delay_ms(2000);

    /* ==================== DDS正弦波测试 ==================== */
    // 启动DDS系统产生连续正弦波
    DDS_DAC904_Status_t dds_status = DDS_DAC904_Start();
    if (dds_status == DDS_DAC904_OK) {
        // DDS启动成功，LED指示（如果有的话）
        // 可以在这里添加LED闪烁或其他指示
        // DDS启动成功，让它运行5秒
        Delay_ms(5000);

        // 测试频率调整
        DDS_DAC904_SetFrequency(2000);  // 2kHz
        Delay_ms(3000);

        DDS_DAC904_SetFrequency(500);   // 500Hz
        Delay_ms(3000);

        DDS_DAC904_SetFrequency(1000);  // 回到1kHz
        Delay_ms(2000);
    } else {
        // DDS启动失败，使用手动正弦波
        // 错误指示：快速闪烁或其他方式
        DAC904_WriteData(16383);  // 最大值指示错误
        Delay_ms(100);
        DAC904_WriteData(0);      // 最小值
        Delay_ms(100);
        DAC904_WriteData(8192);   // 回到中间值
        Delay_ms(500);
        for (int cycle = 0; cycle < 3; cycle++) {  // 3个周期
            // 修复：使用完整的正弦波数据，4V峰峰值，2.5V偏移
            uint16_t sine_points[16] = {
                10650,  // 0°   = 2.5V + 2V*sin(0°)
                12236,  // 22.5°= 2.5V + 2V*sin(22.5°)
                13650,  // 45°  = 2.5V + 2V*sin(45°)
                14746,  // 67.5°= 2.5V + 2V*sin(67.5°)
                15383,  // 90°  = 2.5V + 2V*sin(90°)
                14746,  // 112.5°
                13650,  // 135°
                12236,  // 157.5°
                10650,  // 180°
                9064,   // 202.5°
                7650,   // 225°
                6554,   // 247.5°
                5917,   // 270° = 2.5V + 2V*sin(270°)
                6554,   // 292.5°
                7650,   // 315°
                9064    // 337.5°
            };

            for (int i = 0; i < 16; i++) {
                DAC904_WriteData(sine_points[i]);
                Delay_ms(20);  // 减少到20ms，总周期320ms ≈ 3.125Hz
            }
        }
    }

    /* ==================== 固定3kHz正弦波输出 ==================== */
    // DDS已在初始化时配置为：3kHz正弦波，1V峰峰值，2.5V偏移
    // 无需运行时修改，保持最大稳定性

    while (1) {
        // 完全空循环，让DDS稳定运行
        __NOP();
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  DAC904 DDS系统初始化
  * @param  None
  * @retval None
  */
void DAC904_DDS_Init(void)
{
    // 配置DAC904 DDS参数
    DDS_DAC904_Config_t dds_config = {
        .frequency = current_frequency,         // 3kHz
        .amplitude = 1.0f,                     // 1.0V峰峰值
        .offset = 2.5f,                        // 2.5V直流偏移 (单极性输出)
        .wave_type = current_wave_type,        // 正弦波
        .sample_rate = 50000,  // 50kHz采样率 (平滑度和稳定性平衡)
        .enable_continuous = true,             // 连续输出
        .enable_phase_sync = false             // 不需要相位同步
    };

    // 初始化DDS DAC904
    DDS_DAC904_Status_t status = DDS_DAC904_Init(&dds_config);
    if (status != DDS_DAC904_OK) {
        // 初始化失败，跳过DDS功能，继续执行基本测试
        // 这样可以测试DAC904基本功能
    } else {
        // 立即启动DDS输出
        status = DDS_DAC904_Start();
        if (status != DDS_DAC904_OK) {
            // 启动失败，但继续执行
        }
    }
}

/**
  * @brief  DAC904功能演示
  * @param  None
  * @retval None
  */
void DAC904_DDS_Demo(void)
{
    // 启动DDS输出
    DDS_DAC904_Start();

    // 延时让信号稳定
    Delay_ms(1000);

    // 演示频率调整：1kHz -> 5kHz -> 10kHz -> 1kHz
    DDS_DAC904_SetFrequency(5000);   // 5kHz
    Delay_ms(2000);

    DDS_DAC904_SetFrequency(10000);  // 10kHz
    Delay_ms(2000);

    DDS_DAC904_SetFrequency(1000);   // 回到1kHz
    Delay_ms(1000);

    // 演示幅度调整：2.5V -> 1.0V -> 4.0V -> 2.5V
    DDS_DAC904_SetAmplitude(1.0f);   // 1.0V峰峰值
    Delay_ms(2000);

    DDS_DAC904_SetAmplitude(4.0f);   // 4.0V峰峰值
    Delay_ms(2000);

    DDS_DAC904_SetAmplitude(4.0f);   // 回到4.0V
    Delay_ms(1000);
}

/**
  * @brief  测试不同频率
  * @param  None
  * @retval None
  */
void DAC904_Test_Frequencies(void)
{
    // 测试频率序列：100Hz, 500Hz, 1kHz, 2kHz, 5kHz, 10kHz
    uint32_t test_frequencies[] = {100, 500, 1000, 2000, 5000, 10000};
    uint8_t freq_count = sizeof(test_frequencies) / sizeof(test_frequencies[0]);

    for (uint8_t i = 0; i < freq_count; i++) {
        DDS_DAC904_SetFrequency(test_frequencies[i]);
        current_frequency = test_frequencies[i];
        Delay_ms(3000);  // 每个频率持续3秒
    }

    // 回到默认频率
    DDS_DAC904_SetFrequency(1000);
    current_frequency = 1000;
}

/**
  * @brief  测试不同幅度
  * @param  None
  * @retval None
  */
void DAC904_Test_Amplitudes(void)
{
    // 测试幅度序列：0.5V, 1.0V, 2.0V, 3.0V, 4.0V, 5.0V
    float test_amplitudes[] = {0.5f, 1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
    uint8_t amp_count = sizeof(test_amplitudes) / sizeof(test_amplitudes[0]);

    for (uint8_t i = 0; i < amp_count; i++) {
        DDS_DAC904_SetAmplitude(test_amplitudes[i]);
        // 移除current_amplitude赋值，直接使用DDS函数设置
        Delay_ms(2000);  // 每个幅度持续2秒
    }

    // 回到默认幅度
    DDS_DAC904_SetAmplitude(4.0f);
    // 移除current_amplitude赋值
}

/**
  * @brief  测试不同波形
  * @param  None
  * @retval None
  */
void DAC904_Test_Waveforms(void)
{
    // 测试波形序列：正弦波、方波、三角波、锯齿波
    Wave_Type_14bit_t test_waveforms[] = {
        WAVE_TYPE_SINE,
        WAVE_TYPE_SQUARE,
        WAVE_TYPE_TRIANGLE,
        WAVE_TYPE_SAWTOOTH
    };
    uint8_t wave_count = sizeof(test_waveforms) / sizeof(test_waveforms[0]);

    for (uint8_t i = 0; i < wave_count; i++) {
        DDS_DAC904_SetWaveType(test_waveforms[i]);
        current_wave_type = test_waveforms[i];
        Delay_ms(4000);  // 每个波形持续4秒
    }

    // 回到默认波形
    DDS_DAC904_SetWaveType(WAVE_TYPE_SINE);
    current_wave_type = WAVE_TYPE_SINE;
}

/**
  * @brief  DAC904调试状态输出
  * @param  None
  * @retval None
  */
void DAC904_Debug_Status(void)
{
    // 获取DAC904状态
    DAC904_Handle_t* dac_handle = DAC904_GetHandle();
    DDS_DAC904_Handle_t* dds_handle = DDS_DAC904_GetHandle();

    // 简单的状态指示 - 通过LED或者GPIO输出
    // 这里可以添加串口输出或者其他调试方式

    // 测试基本GPIO功能
    if (dac_handle->is_initialized) {
        // 快速闪烁表示DAC904已初始化
        for (int i = 0; i < 5; i++) {
            DAC904_WriteData(16383);  // 最大值
            Delay_ms(100);
            DAC904_WriteData(0);      // 最小值
            Delay_ms(100);
        }
        DAC904_WriteData(8192);  // 回到中间值
    }
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


