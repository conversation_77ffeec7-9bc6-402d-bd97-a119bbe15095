/**
  ******************************************************************************
  * @file    dac904.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   DAC904高速DAC模块驱动实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "dac904.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
  * @brief  DAC904全局句柄
  */
DAC904_Handle_t g_dac904_handle;

/* Private function prototypes -----------------------------------------------*/
static DAC904_Status_t DAC904_GPIO_Config(void);
static DAC904_Status_t DAC904_Timer_Config(uint32_t frequency);
static void DAC904_GPIO_DeConfig(void);
static void DAC904_Timer_DeConfig(void);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DAC904初始化
  * @param  config: 配置参数指针
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_Init(DAC904_Config_t* config)
{
    // 参数检查
    if (config == NULL) {
        return DAC904_PARAM_ERROR;
    }

    // 检查时钟频率范围
    if (config->clock_frequency > DAC904_CLOCK_MAX) {
        return DAC904_PARAM_ERROR;
    }

    // 清零句柄
    memset(&g_dac904_handle, 0, sizeof(DAC904_Handle_t));

    // 保存配置
    g_dac904_handle.config = *config;
    g_dac904_handle.timer = DAC904_TIMER;
    g_dac904_handle.is_initialized = false;
    g_dac904_handle.is_running = false;
    g_dac904_handle.error_count = 0;

    // 配置GPIO
    if (DAC904_GPIO_Config() != DAC904_OK) {
        return DAC904_ERROR;
    }

    // 如果需要定时器模式，配置定时器
    if (config->mode == DAC904_MODE_TIMER && config->clock_frequency > 0) {
        if (DAC904_Timer_Config(config->clock_frequency) != DAC904_OK) {
            DAC904_GPIO_DeConfig();
            return DAC904_ERROR;
        }
    }

    // 初始化完成
    g_dac904_handle.is_initialized = true;

    // 设置初始输出为中间值
    DAC904_WriteData(DAC904_DATA_MID);

    return DAC904_OK;
}

/**
  * @brief  DAC904反初始化
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_DeInit(void)
{
    // 停止定时器
    if (g_dac904_handle.is_running) {
        DAC904_StopTimer();
    }

    // 反初始化定时器
    DAC904_Timer_DeConfig();

    // 反初始化GPIO
    DAC904_GPIO_DeConfig();

    // 清除句柄
    memset(&g_dac904_handle, 0, sizeof(DAC904_Handle_t));

    return DAC904_OK;
}

/**
  * @brief  写入数据到DAC904
  * @param  data: 14位数据值 (0-16383)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_WriteData(uint16_t data)
{
    // 检查初始化状态
    if (!g_dac904_handle.is_initialized) {
        return DAC904_ERROR;
    }

    // 检查数据范围
    if (!DAC904_IS_VALID_DATA(data)) {
        g_dac904_handle.error_count++;
        return DAC904_PARAM_ERROR;
    }

    // 写入并行数据
    DAC904_SET_DATA_FAST(data);

    // 产生时钟脉冲 (如果使能时钟输出)
    if (g_dac904_handle.config.enable_clock_output) {
        // 确保数据建立时间
        __NOP(); __NOP(); __NOP(); __NOP();
        __NOP(); __NOP(); __NOP(); __NOP();

        // 产生上升沿锁存数据
        DAC904_CLK_HIGH();

        // 保持高电平一段时间
        __NOP(); __NOP(); __NOP(); __NOP();
        __NOP(); __NOP(); __NOP(); __NOP();
        __NOP(); __NOP(); __NOP(); __NOP();

        // 下降沿
        DAC904_CLK_LOW();

        // 保持低电平
        __NOP(); __NOP(); __NOP(); __NOP();
    }

    return DAC904_OK;
}

/**
  * @brief  设置DAC904输出电压
  * @param  voltage: 输出电压值 (0.0V - 5.0V)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_SetVoltage(float voltage)
{
    // 检查电压范围
    if (voltage < 0.0f || voltage > 5.0f) {
        return DAC904_PARAM_ERROR;
    }

    // 转换为DAC数据
    uint16_t dac_data = DAC904_VOLTAGE_TO_DATA(voltage);

    // 写入DAC
    return DAC904_WriteData(dac_data);
}

/**
  * @brief  启动DAC904定时器模式
  * @param  frequency: 更新频率 (Hz)
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_StartTimer(uint32_t frequency)
{
    // 检查初始化状态
    if (!g_dac904_handle.is_initialized) {
        return DAC904_ERROR;
    }

    // 检查频率范围
    if (frequency == 0 || frequency > DAC904_CLOCK_MAX) {
        return DAC904_PARAM_ERROR;
    }

    // 如果已经运行，先停止
    if (g_dac904_handle.is_running) {
        DAC904_StopTimer();
    }

    // 重新配置定时器频率
    if (DAC904_Timer_Config(frequency) != DAC904_OK) {
        return DAC904_ERROR;
    }

    // 启动定时器
    TIM_Cmd(g_dac904_handle.timer, ENABLE);
    g_dac904_handle.is_running = true;

    return DAC904_OK;
}

/**
  * @brief  停止DAC904定时器模式
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
DAC904_Status_t DAC904_StopTimer(void)
{
    // 检查初始化状态
    if (!g_dac904_handle.is_initialized) {
        return DAC904_ERROR;
    }

    // 停止定时器
    if (g_dac904_handle.timer != NULL) {
        TIM_Cmd(g_dac904_handle.timer, DISABLE);
    }

    g_dac904_handle.is_running = false;

    return DAC904_OK;
}

/**
  * @brief  获取DAC904状态
  * @param  None
  * @retval DAC904_Handle_t* 句柄指针
  */
DAC904_Handle_t* DAC904_GetHandle(void)
{
    return &g_dac904_handle;
}

/**
  * @brief  DAC904定时器中断回调函数
  * @param  None
  * @retval None
  * @note   此函数需要在定时器中断中调用
  */
void DAC904_TimerCallback(void)
{
    // 此函数将在DDS模块中实现具体的波形生成逻辑
    // 这里只是一个占位符，确保定时器中断能够正常工作
    
    // 清除定时器中断标志
    if (TIM_GetITStatus(g_dac904_handle.timer, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(g_dac904_handle.timer, TIM_IT_Update);
    }
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  配置DAC904 GPIO
  * @param  None
  * @retval DAC904_Status_t 操作状态
  */
static DAC904_Status_t DAC904_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOE时钟 (在BSP_Init中已使能)
    RCC_AHB1PeriphClockCmd(DAC904_DATA_CLK, ENABLE);

    // 配置数据引脚 (PE0-PE13)
    GPIO_InitStructure.GPIO_Pin = DAC904_DATA_PINS_MASK;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = (GPIOSpeed_TypeDef)g_dac904_handle.config.gpio_speed;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_DATA_PORT, &GPIO_InitStructure);

    // 配置时钟引脚 (PE14)
    GPIO_InitStructure.GPIO_Pin = DAC904_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = (GPIOSpeed_TypeDef)g_dac904_handle.config.gpio_speed;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_CLOCK_PORT, &GPIO_InitStructure);

    // 初始化引脚状态
    GPIO_ResetBits(DAC904_DATA_PORT, DAC904_DATA_PINS_MASK);  // 数据引脚清零
    GPIO_ResetBits(DAC904_CLOCK_PORT, DAC904_CLK_PIN);        // 时钟引脚清零

    return DAC904_OK;
}

/**
  * @brief  配置DAC904定时器
  * @param  frequency: 定时器频率 (Hz)
  * @retval DAC904_Status_t 操作状态
  */
static DAC904_Status_t DAC904_Timer_Config(uint32_t frequency)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能定时器时钟
    RCC_APB1PeriphClockCmd(DAC904_TIMER_CLK, ENABLE);

    // 计算定时器参数
    uint32_t timer_clock = 84000000;  // APB1时钟84MHz
    uint32_t period = timer_clock / frequency;

    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Period = period - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(g_dac904_handle.timer, &TIM_TimeBaseStructure);

    // 使能定时器中断
    TIM_ITConfig(g_dac904_handle.timer, TIM_IT_Update, ENABLE);

    // 配置NVIC - 最高优先级确保DDS稳定性
    NVIC_InitStructure.NVIC_IRQChannel = DAC904_TIMER_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;  // 最高优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    return DAC904_OK;
}

/**
  * @brief  反初始化DAC904 GPIO
  * @param  None
  * @retval None
  */
static void DAC904_GPIO_DeConfig(void)
{
    // 将GPIO配置为输入模式
    GPIO_InitTypeDef GPIO_InitStructure;
    
    GPIO_InitStructure.GPIO_Pin = DAC904_DATA_PINS_MASK | DAC904_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC904_DATA_PORT, &GPIO_InitStructure);
}

/**
  * @brief  反初始化DAC904定时器
  * @param  None
  * @retval None
  */
static void DAC904_Timer_DeConfig(void)
{
    // 禁用定时器
    if (g_dac904_handle.timer != NULL) {
        TIM_Cmd(g_dac904_handle.timer, DISABLE);
        TIM_ITConfig(g_dac904_handle.timer, TIM_IT_Update, DISABLE);
    }

    // 禁用NVIC
    NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = DAC904_TIMER_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelCmd = DISABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/************************ (C) COPYRIGHT DAC904移植项目组 *****END OF FILE****/
