<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_opt.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>VirtualCOMPort</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>8000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>..\LIS\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <Books>
        <Book>
          <Number>0</Number>
          <Title>Reference Manual</Title>
          <Path>DATASHTS\ST\STM32F10xxx.PDF</Path>
        </Book>
      </Books>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>0</tRtrace>
        <sRSysVw>0</sRSysVw>
        <tRSysVw>0</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>6</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>Segger\JL2CM3.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(100=-1,-1,-1,-1,0)(110=-1,-1,-1,-1,0)(111=-1,-1,-1,-1,0)(1011=-1,-1,-1,-1,0)(180=-1,-1,-1,-1,0)(120=-1,-1,-1,-1,0)(121=-1,-1,-1,-1,0)(122=-1,-1,-1,-1,0)(123=-1,-1,-1,-1,0)(124=-1,-1,-1,-1,0)(125=-1,-1,-1,-1,0)(126=-1,-1,-1,-1,0)(140=-1,-1,-1,-1,0)(240=-1,-1,-1,-1,0)(190=-1,-1,-1,-1,0)(200=-1,-1,-1,-1,0)(170=-1,-1,-1,-1,0)(130=-1,-1,-1,-1,0)(131=-1,-1,-1,-1,0)(132=-1,-1,-1,-1,0)(133=-1,-1,-1,-1,0)(160=-1,-1,-1,-1,0)(161=-1,-1,-1,-1,0)(162=-1,-1,-1,-1,0)(210=-1,-1,-1,-1,0)(211=-1,-1,-1,-1,0)(220=-1,-1,-1,-1,0)(221=-1,-1,-1,-1,0)(230=-1,-1,-1,-1,0)(231=-1,-1,-1,-1,0)(232=-1,-1,-1,-1,0)(233=-1,-1,-1,-1,0)(150=-1,-1,-1,-1,0)(151=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name>(105=-1,-1,-1,-1,0)(106=-1,-1,-1,-1,0)(107=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-U805306163 -O78 -S9 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -N00("ARM CoreSight SW-DP") -D00(1BA01477) -L00(4) -TO18 -********** -TP21 -TDS8004 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TRE0 -FO15 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL080000</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGDARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(100=-1,-1,-1,-1,0)(110=-1,-1,-1,-1,0)(111=-1,-1,-1,-1,0)(1011=-1,-1,-1,-1,0)(180=-1,-1,-1,-1,0)(120=-1,-1,-1,-1,0)(121=-1,-1,-1,-1,0)(122=-1,-1,-1,-1,0)(123=-1,-1,-1,-1,0)(124=-1,-1,-1,-1,0)(125=-1,-1,-1,-1,0)(126=-1,-1,-1,-1,0)(140=-1,-1,-1,-1,0)(240=-1,-1,-1,-1,0)(190=-1,-1,-1,-1,0)(200=-1,-1,-1,-1,0)(170=-1,-1,-1,-1,0)(130=-1,-1,-1,-1,0)(131=-1,-1,-1,-1,0)(132=-1,-1,-1,-1,0)(133=-1,-1,-1,-1,0)(160=-1,-1,-1,-1,0)(161=-1,-1,-1,-1,0)(162=-1,-1,-1,-1,0)(210=-1,-1,-1,-1,0)(211=-1,-1,-1,-1,0)(220=-1,-1,-1,-1,0)(221=-1,-1,-1,-1,0)(230=-1,-1,-1,-1,0)(234=-1,-1,-1,-1,0)(231=-1,-1,-1,-1,0)(232=-1,-1,-1,-1,0)(233=-1,-1,-1,-1,0)(150=-1,-1,-1,-1,0)(151=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name>-T0</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-O14 -S0 -C0 -N00("ARM Cortex-M3") -D00(1BA00477) -L00(4) -FO7 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL080000</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>CMSIS</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\CORE\core_cm3.c</PathWithFileName>
      <FilenameWithoutPath>core_cm3.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\STARTUP\startup_stm32f10x_hd.s</PathWithFileName>
      <FilenameWithoutPath>startup_stm32f10x_hd.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>1</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\stm32f10x_it.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_it.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
      <WindowPosition>
        <length>44</length>
        <flags>2</flags>
        <showCmd>3</showCmd>
        <MinPosition>
          <xPos>-1</xPos>
          <yPos>-1</yPos>
        </MinPosition>
        <MaxPosition>
          <xPos>-8</xPos>
          <yPos>-30</yPos>
        </MaxPosition>
        <NormalPosition>
          <Top>75</Top>
          <Left>75</Left>
          <Right>966</Right>
          <Bottom>360</Bottom>
        </NormalPosition>
      </WindowPosition>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\system_stm32f10x.c</PathWithFileName>
      <FilenameWithoutPath>system_stm32f10x.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>FWLIB</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\misc.c</PathWithFileName>
      <FilenameWithoutPath>misc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_exti.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_exti.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_fsmc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_fsmc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_gpio.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_rcc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_rcc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_usart.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>SYSTEM</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\delay\delay.c</PathWithFileName>
      <FilenameWithoutPath>delay.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\sys\sys.c</PathWithFileName>
      <FilenameWithoutPath>sys.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\usart\usart.c</PathWithFileName>
      <FilenameWithoutPath>usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>USBLIB</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_core.c</PathWithFileName>
      <FilenameWithoutPath>usb_core.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_init.c</PathWithFileName>
      <FilenameWithoutPath>usb_init.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_int.c</PathWithFileName>
      <FilenameWithoutPath>usb_int.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_mem.c</PathWithFileName>
      <FilenameWithoutPath>usb_mem.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_regs.c</PathWithFileName>
      <FilenameWithoutPath>usb_regs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\USBLIB\src\usb_sil.c</PathWithFileName>
      <FilenameWithoutPath>usb_sil.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>USBConfig</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\hw_config.c</PathWithFileName>
      <FilenameWithoutPath>hw_config.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
      <WindowPosition>
        <length>44</length>
        <flags>0</flags>
        <showCmd>1</showCmd>
        <MinPosition>
          <xPos>-1</xPos>
          <yPos>-1</yPos>
        </MinPosition>
        <MaxPosition>
          <xPos>-8</xPos>
          <yPos>-30</yPos>
        </MaxPosition>
        <NormalPosition>
          <Top>25</Top>
          <Left>25</Left>
          <Right>891</Right>
          <Bottom>381</Bottom>
        </NormalPosition>
      </WindowPosition>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\usb_desc.c</PathWithFileName>
      <FilenameWithoutPath>usb_desc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\usb_endp.c</PathWithFileName>
      <FilenameWithoutPath>usb_endp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\usb_istr.c</PathWithFileName>
      <FilenameWithoutPath>usb_istr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\usb_prop.c</PathWithFileName>
      <FilenameWithoutPath>usb_prop.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\USB\Mass_Storage\src\usb_pwr.c</PathWithFileName>
      <FilenameWithoutPath>usb_pwr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>USER</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
      <WindowPosition>
        <length>44</length>
        <flags>0</flags>
        <showCmd>1</showCmd>
        <MinPosition>
          <xPos>-1</xPos>
          <yPos>-1</yPos>
        </MinPosition>
        <MaxPosition>
          <xPos>-8</xPos>
          <yPos>-30</yPos>
        </MaxPosition>
        <NormalPosition>
          <Top>0</Top>
          <Left>0</Left>
          <Right>912</Right>
          <Bottom>271</Bottom>
        </NormalPosition>
      </WindowPosition>
    </File>
  </Group>

  <Group>
    <GroupName>HARDWARE</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\HARDWARE\LED\led.c</PathWithFileName>
      <FilenameWithoutPath>led.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\HARDWARE\tft\fontupd.c</PathWithFileName>
      <FilenameWithoutPath>fontupd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\HARDWARE\tft\lcd.c</PathWithFileName>
      <FilenameWithoutPath>lcd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\HARDWARE\tft\lcd_drive.c</PathWithFileName>
      <FilenameWithoutPath>lcd_drive.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
