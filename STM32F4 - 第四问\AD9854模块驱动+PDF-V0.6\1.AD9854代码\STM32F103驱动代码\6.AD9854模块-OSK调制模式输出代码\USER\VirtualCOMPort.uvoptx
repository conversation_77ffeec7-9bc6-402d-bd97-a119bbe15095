<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>VirtualCOMPort</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>..\LIS\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>18</CpuCode>
      <Books>
        <Book>
          <Number>0</Number>
          <Title>Quick Start Guide (MCBSTM32E)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Boards/Keil/MCBSTM32E/Documentation/STM32E_QSG.pdf</Path>
        </Book>
        <Book>
          <Number>1</Number>
          <Title>Base Board Schematics (MCBSTM32E)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Boards/Keil/MCBSTM32E/Documentation/mcbstm32e-base-board-schematics.pdf</Path>
        </Book>
        <Book>
          <Number>2</Number>
          <Title>Display Board Schematics (MCBSTM32E)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Boards/Keil/MCBSTM32E/Documentation/mcbstm32e-display-board-schematics.pdf</Path>
        </Book>
        <Book>
          <Number>3</Number>
          <Title>User Manual (MCBSTM32E)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Boards/Keil/MCBSTM32E/Documentation/mcbstm32e.chm</Path>
        </Book>
        <Book>
          <Number>4</Number>
          <Title>User Manual (NUCLEO-F103RB)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32NUCLEO_BSP\1.2.0\Documents\DM00105823.pdf</Path>
        </Book>
        <Book>
          <Number>5</Number>
          <Title>Getting started (NUCLEO-F103RB)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32NUCLEO_BSP\1.2.0\Documents\DM00105925.pdf</Path>
        </Book>
        <Book>
          <Number>6</Number>
          <Title>Schematics (NUCLEO-F103RB)</Title>
          <Path>E:\Keil_v5\ARM\PACK\Keil\STM32NUCLEO_BSP\1.2.0\Documents\MB1136.pdf</Path>
        </Book>
        <Book>
          <Number>7</Number>
          <Title>MCBSTM32E Evaluation Board Web Page (MCBSTM32E)</Title>
          <Path>http://www.keil.com/mcbstm32e/</Path>
        </Book>
        <Book>
          <Number>8</Number>
          <Title>Nucleo STM32 F1 series development board (NUCLEO-F103RB)</Title>
          <Path>http://www.st.com/web/en/catalog/tools/PF259875</Path>
        </Book>
      </Books>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>0</tRtrace>
        <sRSysVw>0</sRSysVw>
        <tRSysVw>0</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>6</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>Segger\JL2CM3.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-********* -O78 -S1 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -RST0 -TO18 -********** -TP21 -TDS8001 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TFE0 -FO31 -********** -FC800 -FN1 -FF0STM32F10x_512.FLM -********** -FL080000 -FP0($$Device:STM32F103RC$Flash/STM32F10x_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGDARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(100=-1,-1,-1,-1,0)(110=-1,-1,-1,-1,0)(111=-1,-1,-1,-1,0)(1011=-1,-1,-1,-1,0)(180=-1,-1,-1,-1,0)(120=-1,-1,-1,-1,0)(121=-1,-1,-1,-1,0)(122=-1,-1,-1,-1,0)(123=-1,-1,-1,-1,0)(124=-1,-1,-1,-1,0)(125=-1,-1,-1,-1,0)(126=-1,-1,-1,-1,0)(140=-1,-1,-1,-1,0)(240=-1,-1,-1,-1,0)(190=-1,-1,-1,-1,0)(200=-1,-1,-1,-1,0)(170=-1,-1,-1,-1,0)(130=-1,-1,-1,-1,0)(131=-1,-1,-1,-1,0)(132=-1,-1,-1,-1,0)(133=-1,-1,-1,-1,0)(160=-1,-1,-1,-1,0)(161=-1,-1,-1,-1,0)(162=-1,-1,-1,-1,0)(210=-1,-1,-1,-1,0)(211=-1,-1,-1,-1,0)(220=-1,-1,-1,-1,0)(221=-1,-1,-1,-1,0)(230=-1,-1,-1,-1,0)(234=-1,-1,-1,-1,0)(231=-1,-1,-1,-1,0)(232=-1,-1,-1,-1,0)(233=-1,-1,-1,-1,0)(150=-1,-1,-1,-1,0)(151=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name>-T0</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-U-O14 -O14 -S0 -C0 -P00 -N00("ARM Cortex-M3") -D00(1BA00477) -L00(4) -TO18 -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO15  -FN1 -FC1000 -********** -FF0STM32F10x_512 -FL080000 -********** -FP0($$Device:STM32F103RC$Flash/STM32F10x_512.FLM)</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint>
        <Bp>
          <Number>0</Number>
          <Type>0</Type>
          <LineNumber>334</LineNumber>
          <EnabledFlag>1</EnabledFlag>
          <Address>*********</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\company\kangwei\ArrangeModule\绋嬪簭鍖?AD9954 DDS寮€鍙戞澘-鐐归9854\Soft\Task\task_manage.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\\VirtualCOMPort\../Soft/Task/task_manage.c\334</Expression>
        </Bp>
        <Bp>
          <Number>1</Number>
          <Type>0</Type>
          <LineNumber>317</LineNumber>
          <EnabledFlag>1</EnabledFlag>
          <Address>*********</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\company\kangwei\ArrangeModule\绋嬪簭鍖?AD9954 DDS寮€鍙戞澘-鐐归9854\Soft\Task\task_manage.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\\VirtualCOMPort\../Soft/Task/task_manage.c\317</Expression>
        </Bp>
      </Breakpoint>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>SweepTime,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>fre_buf</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>SweepStepFre,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>P_Index</ItemText>
        </Ww>
      </WatchWindow1>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <DebugDescription>
        <Enable>1</Enable>
        <EnableLog>0</EnableLog>
        <Protocol>2</Protocol>
        <DbgClock>10000000</DbgClock>
      </DebugDescription>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>CMSIS</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\CORE\core_cm3.c</PathWithFileName>
      <FilenameWithoutPath>core_cm3.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\stm32f10x_it.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_it.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
      <WindowPosition>
        <length>44</length>
        <flags>2</flags>
        <showCmd>3</showCmd>
        <MinPosition>
          <xPos>-1</xPos>
          <yPos>-1</yPos>
        </MinPosition>
        <MaxPosition>
          <xPos>-8</xPos>
          <yPos>-30</yPos>
        </MaxPosition>
        <NormalPosition>
          <Top>75</Top>
          <Left>75</Left>
          <Right>966</Right>
          <Bottom>360</Bottom>
        </NormalPosition>
      </WindowPosition>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\system_stm32f10x.c</PathWithFileName>
      <FilenameWithoutPath>system_stm32f10x.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\CM3\STARTUP\startup_stm32f10x_hd.s</PathWithFileName>
      <FilenameWithoutPath>startup_stm32f10x_hd.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>FWLIB</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\misc.c</PathWithFileName>
      <FilenameWithoutPath>misc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_exti.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_exti.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_fsmc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_fsmc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_gpio.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_rcc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_rcc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_usart.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_adc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_adc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_bkp.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_bkp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_can.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_can.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_cec.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_cec.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_crc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_dac.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_dac.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_dbgmcu.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_dbgmcu.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_dma.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_dma.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_flash.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_flash.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_i2c.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_i2c.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_iwdg.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_iwdg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_pwr.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_pwr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_rtc.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_sdio.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_sdio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_spi.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_tim.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_tim.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\CMSIS\STM32LIB\src\stm32f10x_wwdg.c</PathWithFileName>
      <FilenameWithoutPath>stm32f10x_wwdg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>SYSTEM</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\delay\delay.c</PathWithFileName>
      <FilenameWithoutPath>delay.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\sys\sys.c</PathWithFileName>
      <FilenameWithoutPath>sys.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\SYSTEM\usart\usart.c</PathWithFileName>
      <FilenameWithoutPath>usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>USER</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>31</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
      <WindowPosition>
        <length>44</length>
        <flags>0</flags>
        <showCmd>1</showCmd>
        <MinPosition>
          <xPos>-1</xPos>
          <yPos>-1</yPos>
        </MinPosition>
        <MaxPosition>
          <xPos>-8</xPos>
          <yPos>-30</yPos>
        </MaxPosition>
        <NormalPosition>
          <Top>0</Top>
          <Left>0</Left>
          <Right>912</Right>
          <Bottom>271</Bottom>
        </NormalPosition>
      </WindowPosition>
    </File>
  </Group>

  <Group>
    <GroupName>HARDWARE</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>32</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\HARDWARE\AD9854\AD9854.c</PathWithFileName>
      <FilenameWithoutPath>AD9854.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
